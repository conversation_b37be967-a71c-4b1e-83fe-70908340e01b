"""
基于位移数据的实时LSTM+卡尔曼滤波预测演示
专门针对振动位移信号进行实时预测优化
"""

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
import time
from collections import deque
import warnings
import random

def set_all_seeds(seed=42):
    """设置所有随机种子以确保结果可重复"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

# 设置随机种子
set_all_seeds(42)

# 忽略警告
warnings.filterwarnings("ignore")

# 设置中文字体
try:
    # 基于字体测试结果，使用SimHei字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    USE_CHINESE = True
    print("中文字体设置成功: SimHei")
except:
    plt.rcParams['axes.unicode_minus'] = False
    USE_CHINESE = False
    print("使用默认字体")

# 位移专用LSTM模型
class DisplacementLSTM(nn.Module):
    def __init__(self, input_size=1, hidden_size=64, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.linear = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        # x shape: (batch_size, seq_len, input_size)
        lstm_out, _ = self.lstm(x)
        # 取最后一个时间步的输出
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        prediction = self.linear(out)
        return prediction

# 集成LSTM的自适应卡尔曼滤波器
class LSTMKalmanFilter:
    def __init__(self, lstm_model, scaler, window_size=25, initial_process_variance=1e-4, initial_measurement_variance=5e-4):
        """
        在卡尔曼滤波的预测步骤中集成LSTM模型
        """
        # LSTM相关参数
        self.lstm_model = lstm_model
        self.scaler = scaler
        self.window_size = window_size
        self.lstm_model.eval()

        # 卡尔曼滤波参数
        self.Q = initial_process_variance  # 过程噪声方差
        self.R = initial_measurement_variance  # 测量噪声方差
        self.x_hat = 0.0  # 状态估计值
        self.P = 1.0      # 估计误差协方差

        # LSTM输入数据缓冲区
        self.lstm_buffer = deque(maxlen=window_size)

        # 历史数据用于自适应调整
        self.measurement_history = []
        self.innovation_history = []
        self.max_history = 15

        # 自适应参数
        self.displacement_threshold = 2e-3
        self.adaptation_rate = 0.08

        # 初始化标志
        self.is_initialized = False

    def initialize_lstm_buffer(self, initial_data):
        """初始化LSTM缓冲区"""
        if len(initial_data) < self.window_size:
            raise ValueError(f"初始数据长度必须至少为 {self.window_size}")

        # 标准化初始数据并填充缓冲区
        normalized_data = self.scaler.transform(
            np.array(initial_data[-self.window_size:]).reshape(-1, 1)
        ).flatten()

        self.lstm_buffer.clear()
        for value in normalized_data:
            self.lstm_buffer.append(value)

        self.is_initialized = True
        print(f"LSTM-卡尔曼滤波器缓冲区已初始化，包含 {len(self.lstm_buffer)} 个数据点")

    def predict(self):
        """预测步骤：使用LSTM进行状态预测"""
        if not self.is_initialized or len(self.lstm_buffer) < self.window_size:
            # 如果LSTM未初始化，使用简单的线性预测
            self.x_hat = self.x_hat  # 保持当前状态
            self.P = self.P + self.Q
            return self.x_hat

        # 使用LSTM进行预测
        try:
            # 准备LSTM输入序列
            input_seq = torch.FloatTensor(list(self.lstm_buffer)).unsqueeze(0).unsqueeze(-1)

            # LSTM预测
            with torch.no_grad():
                normalized_prediction = self.lstm_model(input_seq).item()

            # 反标准化得到实际预测值
            lstm_prediction = self.scaler.inverse_transform([[normalized_prediction]])[0, 0]

            # 使用LSTM预测作为状态预测
            self.x_hat = lstm_prediction
            self.P = self.P + self.Q

            return self.x_hat

        except Exception as e:
            print(f"LSTM预测出错: {e}")
            # 出错时使用简单预测
            self.x_hat = self.x_hat
            self.P = self.P + self.Q
            return self.x_hat

    def update(self, measurement):
        """更新步骤：使用观测值更新状态估计"""
        # 记录测量历史
        self.measurement_history.append(measurement)
        if len(self.measurement_history) > self.max_history:
            self.measurement_history.pop(0)

        # 计算创新（观测值与预测值的差异）
        innovation = measurement - self.x_hat
        self.innovation_history.append(innovation)
        if len(self.innovation_history) > self.max_history:
            self.innovation_history.pop(0)

        # 自适应调整
        self._adaptive_adjustment()

        # 卡尔曼增益
        K = self.P / (self.P + self.R)

        # 状态更新
        self.x_hat = self.x_hat + K * innovation
        self.P = (1 - K) * self.P

        # 更新LSTM缓冲区（用于下次预测）
        if self.is_initialized:
            # 将观测值标准化后添加到LSTM缓冲区
            normalized_measurement = self.scaler.transform([[measurement]])[0, 0]
            self.lstm_buffer.append(normalized_measurement)

        return self.x_hat

    def _adaptive_adjustment(self):
        """基于信号特性的自适应调整"""
        if len(self.measurement_history) < 3:
            return

        recent_measurements = np.array(self.measurement_history[-3:])

        # 计算位移变化程度
        displacement_changes = np.abs(np.diff(recent_measurements))
        avg_change = np.mean(displacement_changes)
        max_change = np.max(displacement_changes)

        # 根据变化程度调整过程噪声
        if max_change > self.displacement_threshold * 2:
            # 检测到较大变化，增加过程噪声
            self.Q = min(5e-3, self.Q * 1.2)
        elif avg_change > self.displacement_threshold:
            # 变化适中，轻微增加过程噪声
            self.Q = min(2e-3, self.Q * 1.05)
        else:
            # 变化较小，减少过程噪声
            self.Q = max(1e-6, self.Q * 0.99)

        # 基于创新序列调整测量噪声
        if len(self.innovation_history) >= 3:
            innovation_var = np.var(self.innovation_history[-3:])
            if innovation_var > 0:
                target_R = innovation_var * 0.6
                self.R = (1 - self.adaptation_rate) * self.R + self.adaptation_rate * target_R
                self.R = np.clip(self.R, 1e-6, 1e-2)

# LSTM-卡尔曼集成实时预测器
class LSTMKalmanRealTimePredictor:
    def __init__(self, model, scaler, window_size=25, use_kalman=True):
        """
        LSTM-卡尔曼集成实时预测器
        在卡尔曼滤波的预测步骤中使用LSTM
        """
        self.model = model
        self.scaler = scaler
        self.window_size = window_size
        self.use_kalman = use_kalman
        self.model.eval()

        # LSTM-卡尔曼滤波器
        if self.use_kalman:
            self.kalman_filter = LSTMKalmanFilter(
                lstm_model=model,
                scaler=scaler,
                window_size=window_size,
                initial_process_variance=1e-4,
                initial_measurement_variance=5e-4
            )
        else:
            # 如果不使用卡尔曼滤波，仍需要数据缓冲区用于LSTM预测
            self.data_buffer = deque(maxlen=window_size)

        # 性能统计
        self.prediction_times = []
        self.predictions_made = 0

    def initialize_buffer(self, initial_data):
        """初始化LSTM-卡尔曼滤波器"""
        if len(initial_data) < self.window_size:
            raise ValueError(f"初始数据长度必须至少为 {self.window_size}")

        if self.use_kalman:
            # 初始化LSTM-卡尔曼滤波器
            self.kalman_filter.initialize_lstm_buffer(initial_data)
            # 设置初始状态估计
            self.kalman_filter.x_hat = initial_data[-1]
        else:
            # 如果不使用卡尔曼滤波，初始化LSTM数据缓冲区
            normalized_data = self.scaler.transform(
                np.array(initial_data[-self.window_size:]).reshape(-1, 1)
            ).flatten()

            self.data_buffer.clear()
            for value in normalized_data:
                self.data_buffer.append(value)

        print(f"LSTM-卡尔曼预测器已初始化")

    def predict_next(self, new_observation=None):
        """
        预测下一个位移值
        new_observation: 新的观测值（如果有的话）
        """
        start_time = time.time()

        if self.use_kalman:
            # 首先获取纯LSTM预测（用于对比显示）
            pure_lstm_prediction = self._get_pure_lstm_prediction()

            # 使用LSTM-卡尔曼滤波器进行预测
            # 1. 预测步骤：LSTM预测下一个状态
            kalman_prediction = self.kalman_filter.predict()

            # 2. 如果有新观测值，进行更新步骤
            if new_observation is not None:
                kalman_prediction = self.kalman_filter.update(new_observation)

            final_prediction = kalman_prediction

        else:
            # 不使用卡尔曼滤波，直接使用LSTM预测
            if new_observation is not None:
                normalized_point = self.scaler.transform([[new_observation]])[0, 0]
                self.data_buffer.append(normalized_point)

            if len(self.data_buffer) < self.window_size:
                raise ValueError(f"缓冲区数据不足，需要 {self.window_size} 个点")

            # 准备输入序列
            input_seq = torch.FloatTensor(list(self.data_buffer)).unsqueeze(0).unsqueeze(-1)

            # LSTM预测
            with torch.no_grad():
                normalized_prediction = self.model(input_seq).item()

            # 反标准化
            pure_lstm_prediction = self.scaler.inverse_transform([[normalized_prediction]])[0, 0]
            final_prediction = pure_lstm_prediction
            kalman_prediction = pure_lstm_prediction

        # 记录性能
        prediction_time = time.time() - start_time
        self.prediction_times.append(prediction_time)
        self.predictions_made += 1

        return pure_lstm_prediction, final_prediction, prediction_time

    def _get_pure_lstm_prediction(self):
        """获取纯LSTM预测（不经过卡尔曼滤波）"""
        if not self.kalman_filter.is_initialized or len(self.kalman_filter.lstm_buffer) < self.window_size:
            return 0.0

        try:
            # 准备LSTM输入序列
            input_seq = torch.FloatTensor(list(self.kalman_filter.lstm_buffer)).unsqueeze(0).unsqueeze(-1)

            # LSTM预测
            with torch.no_grad():
                normalized_prediction = self.model(input_seq).item()

            # 反标准化
            pure_lstm_prediction = self.scaler.inverse_transform([[normalized_prediction]])[0, 0]
            return pure_lstm_prediction

        except Exception as e:
            print(f"纯LSTM预测出错: {e}")
            return 0.0
    
    def get_performance_stats(self):
        """获取性能统计"""
        if not self.prediction_times:
            return None
        
        return {
            'total_predictions': self.predictions_made,
            'avg_prediction_time': np.mean(self.prediction_times),
            'max_prediction_time': np.max(self.prediction_times),
            'min_prediction_time': np.min(self.prediction_times),
            'predictions_per_second': 1.0 / np.mean(self.prediction_times)
        }

def create_sequences(data, window_size):
    """创建训练序列"""
    sequences = []
    targets = []
    
    for i in range(len(data) - window_size):
        seq = data[i:i+window_size]
        target = data[i+window_size]
        sequences.append(seq)
        targets.append(target)
    
    return np.array(sequences), np.array(targets)

def read_excel_file(filename):
    """读取Excel文件中的x坐标数据"""
    try:
        # 尝试使用pandas读取Excel文件
        df = pd.read_excel(filename)

        # 假设x坐标在第一列
        if len(df.columns) > 0:
            x_data = df.iloc[:, 0].dropna().values.astype(float)
            return x_data
        else:
            print("Excel文件中没有找到数据列")
            return None

    except Exception as e:
        print(f"读取Excel文件时出错: {e}")

        # 如果pandas读取失败，尝试其他方法
        try:
            import xlrd
            workbook = xlrd.open_workbook(filename)
            sheet = workbook.sheet_by_index(0)

            # 假设x坐标在第一列（索引0）
            x_data = []
            for row in range(1, sheet.nrows):  # 跳过标题行
                try:
                    value = sheet.cell_value(row, 0)
                    if isinstance(value, (int, float)):
                        x_data.append(float(value))
                except:
                    continue

            return np.array(x_data) if x_data else None

        except ImportError:
            print("需要安装xlrd或openpyxl包来读取Excel文件")
            return None
        except Exception as e2:
            print(f"使用xlrd读取Excel文件时也出错: {e2}")
            return None

def displacement_real_time_demo():
    """flight_tt.xls x坐标数据实时预测演示"""
    print("=== flight_tt.xls x坐标数据LSTM-卡尔曼集成预测演示 ===\n")
    print("在卡尔曼滤波的预测步骤中使用LSTM模型")

    # 1. 加载Excel文件中的x坐标数据
    print("正在读取flight_tt.xls文件...")
    displacement_data = read_excel_file('flight_tt.xls')

    if displacement_data is None:
        print("错误: 无法读取 flight_tt.xls 文件")
        print("请检查文件是否存在或安装相应的Excel读取包")
        return

    if len(displacement_data) == 0:
        print("错误: 从Excel文件中未读取到有效的x坐标数据")
        return

    print(f"\nflight_tt.xls x坐标数据加载成功:")
    print(f"  数据点数: {len(displacement_data)}")
    print(f"  x坐标范围: {np.min(displacement_data):.4f} ~ {np.max(displacement_data):.4f}")
    print(f"  x坐标标准差: {np.std(displacement_data):.4f}")
    print(f"  峰峰值: {np.max(displacement_data) - np.min(displacement_data):.4f}")
    print(f"  前10个数据点: {displacement_data[:10]}")
    print(f"  数据来源: flight_tt.xls文件")
    
    # 2. 数据预处理
    scaler = MinMaxScaler(feature_range=(-1, 1))
    displacement_normalized = scaler.fit_transform(displacement_data.reshape(-1, 1))
    displacement_normalized = torch.FloatTensor(displacement_normalized.flatten())
    
    # 3. 快速训练平缓振动预测模型
    window_size = 25  # 增大窗口以更好地捕捉平缓振动的规律性
    X, y = create_sequences(displacement_normalized.numpy(), window_size)

    # 转换为PyTorch张量
    X = torch.FloatTensor(X).unsqueeze(-1)  # (batch, seq, feature)
    y = torch.FloatTensor(y)

    # 初始化模型（适中的隐藏层大小处理平缓信号）
    model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.008)  # 适中的学习率

    print(f"\n开始训练LSTM模型（用于卡尔曼滤波预测步骤）...")
    print(f"模型参数: 窗口大小={window_size}, 隐藏层大小=96")

    # 训练LSTM模型
    epochs = 120
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()

        if epoch % 30 == 0:
            print(f'Epoch {epoch:3d}: Loss = {loss.item():.6f}')

    print("LSTM模型训练完成！")
    
    # 4. 实时预测演示
    # 使用前80%的数据作为"历史数据"，后20%模拟"实时数据流"
    split_point = int(len(displacement_data) * 0.8)
    historical_data = displacement_data[:split_point]
    streaming_data = displacement_data[split_point:]
    
    print(f"\n=== 实时预测设置 ===")
    print(f"历史数据长度: {len(historical_data)} 点")
    print(f"实时流数据长度: {len(streaming_data)} 点")
    print(f"数据分割点: 80%")
    
    # 创建LSTM-卡尔曼集成实时预测器
    predictor = LSTMKalmanRealTimePredictor(model, scaler, window_size)
    predictor.initialize_buffer(historical_data)

    # 开始实时预测
    print(f"\n开始LSTM-卡尔曼集成实时预测...")
    print(f"预测方式: 在卡尔曼滤波的预测步骤中使用LSTM")

    real_values = []
    lstm_predictions = []
    kalman_predictions = []
    prediction_times = []

    for i, true_value in enumerate(streaming_data):
        # 预测下一个位移值（LSTM在预测步骤中使用）
        lstm_pred, kalman_pred, pred_time = predictor.predict_next()

        # 记录结果
        real_values.append(true_value)
        lstm_predictions.append(lstm_pred)
        kalman_predictions.append(kalman_pred)
        prediction_times.append(pred_time)

        # 将真实观测值作为测量值输入到卡尔曼滤波器
        if i < len(streaming_data) - 1:
            # 下一次预测时会使用这个观测值进行更新
            predictor.predict_next(true_value)

        # 显示进度
        if (i + 1) % 50 == 0:
            print(f"已处理 {i + 1}/{len(streaming_data)} 个位移点，"
                  f"平均预测时间: {np.mean(prediction_times[-50:]):.4f}秒")
    
    return real_values, lstm_predictions, kalman_predictions, predictor

def visualize_displacement_results(real_values, lstm_predictions, kalman_predictions):
    """可视化LSTM-卡尔曼集成预测结果"""
    print(f"\n生成LSTM-卡尔曼集成预测结果可视化...")

    plt.figure(figsize=(16, 12))

    time_steps = np.arange(len(real_values))

    # 子图1: x坐标预测结果对比
    plt.subplot(3, 1, 1)
    plt.plot(time_steps, real_values, 'b-', label='真实x坐标' if USE_CHINESE else 'Real X Coordinate',
             alpha=0.8, linewidth=2)
    plt.plot(time_steps, lstm_predictions, 'r--', label='LSTM预测' if USE_CHINESE else 'LSTM Prediction',
             alpha=0.7, linewidth=1.5)
    plt.plot(time_steps, kalman_predictions, 'g-', label='LSTM-卡尔曼集成' if USE_CHINESE else 'LSTM-Kalman Integrated',
             alpha=0.9, linewidth=2)

    plt.title('LSTM-卡尔曼集成预测结果对比' if USE_CHINESE else 'LSTM-Kalman Integrated Prediction Comparison',
              fontsize=14, fontweight='bold')
    plt.xlabel('时间步' if USE_CHINESE else 'Time Steps')
    plt.ylabel('x坐标' if USE_CHINESE else 'X Coordinate')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 添加统计信息
    lstm_mae = np.mean(np.abs(np.array(real_values) - np.array(lstm_predictions)))
    kalman_mae = np.mean(np.abs(np.array(real_values) - np.array(kalman_predictions)))

    info_text = f'LSTM MAE: {lstm_mae:.6f}\nLSTM-Kalman MAE: {kalman_mae:.6f}'
    plt.text(0.02, 0.98, info_text, transform=plt.gca().transAxes,
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    # 子图2: 预测误差分析
    plt.subplot(3, 1, 2)
    lstm_errors = np.abs(np.array(real_values) - np.array(lstm_predictions))
    kalman_errors = np.abs(np.array(real_values) - np.array(kalman_predictions))

    plt.plot(time_steps, lstm_errors, 'r-', alpha=0.7, label='LSTM误差' if USE_CHINESE else 'LSTM Error',
             linewidth=1.5)
    plt.plot(time_steps, kalman_errors, 'g-', alpha=0.9, label='LSTM-卡尔曼误差' if USE_CHINESE else 'LSTM-Kalman Error',
             linewidth=2)

    plt.title('预测误差对比' if USE_CHINESE else 'Prediction Error Comparison',
              fontsize=12)
    plt.xlabel('时间步' if USE_CHINESE else 'Time Steps')
    plt.ylabel('绝对误差' if USE_CHINESE else 'Absolute Error')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 子图3: 误差改善分析
    plt.subplot(3, 1, 3)
    error_improvement = lstm_errors - kalman_errors
    plt.plot(time_steps, error_improvement, 'purple', alpha=0.8, linewidth=2)
    plt.axhline(y=0, color='black', linestyle='--', alpha=0.5)

    plt.title('LSTM-卡尔曼集成改善效果' if USE_CHINESE else 'LSTM-Kalman Integration Improvement',
              fontsize=12)
    plt.xlabel('时间步' if USE_CHINESE else 'Time Steps')
    plt.ylabel('误差减少量' if USE_CHINESE else 'Error Reduction')
    plt.grid(True, alpha=0.3)

    # 添加改善统计
    avg_improvement = np.mean(error_improvement)
    improvement_text = f'平均改善: {avg_improvement:.6f}' if USE_CHINESE else f'Avg Improvement: {avg_improvement:.6f}'
    plt.text(0.02, 0.98, improvement_text, transform=plt.gca().transAxes,
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))

    plt.tight_layout()
    plt.savefig('lstm_kalman_integrated_prediction.png', dpi=300, bbox_inches='tight')
    plt.show()

    print(f"预测结果图像已保存到: lstm_kalman_integrated_prediction.png")

    # 保存详细结果
    results_df = pd.DataFrame({
        'time_step': time_steps,
        'real_x_coordinate': real_values,
        'lstm_prediction': lstm_predictions,
        'lstm_kalman_prediction': kalman_predictions,
        'lstm_error': lstm_errors,
        'lstm_kalman_error': kalman_errors,
        'error_improvement': error_improvement
    })

    results_df.to_csv('lstm_kalman_integrated_results.csv', index=False)
    print(f"详细结果已保存到: lstm_kalman_integrated_results.csv")

if __name__ == "__main__":
    results = displacement_real_time_demo()
    
    if results is not None:
        real_values, lstm_predictions, kalman_predictions, predictor = results
        
        # 性能分析
        stats = predictor.get_performance_stats()
        
        print(f"\n=== LSTM-卡尔曼集成预测性能统计 ===")
        print(f"总预测次数: {stats['total_predictions']}")
        print(f"平均预测时间: {stats['avg_prediction_time']:.4f} 秒")
        print(f"预测速率: {stats['predictions_per_second']:.1f} 次/秒")

        # 准确性分析
        lstm_mae = np.mean(np.abs(np.array(real_values) - np.array(lstm_predictions)))
        lstm_rmse = np.sqrt(np.mean((np.array(real_values) - np.array(lstm_predictions)) ** 2))

        kalman_mae = np.mean(np.abs(np.array(real_values) - np.array(kalman_predictions)))
        kalman_rmse = np.sqrt(np.mean((np.array(real_values) - np.array(kalman_predictions)) ** 2))

        print(f"\n=== LSTM-卡尔曼集成预测准确性对比 ===")
        print(f"LSTM单独预测:")
        print(f"  平均绝对误差 (MAE): {lstm_mae:.6f}")
        print(f"  均方根误差 (RMSE): {lstm_rmse:.6f}")
        print(f"LSTM-卡尔曼集成:")
        print(f"  平均绝对误差 (MAE): {kalman_mae:.6f}")
        print(f"  均方根误差 (RMSE): {kalman_rmse:.6f}")

        mae_improvement = ((lstm_mae - kalman_mae) / lstm_mae) * 100
        rmse_improvement = ((lstm_rmse - kalman_rmse) / lstm_rmse) * 100
        print(f"集成方法改善:")
        print(f"  MAE改善: {mae_improvement:.2f}%")
        print(f"  RMSE改善: {rmse_improvement:.2f}%")

        # 可视化结果
        visualize_displacement_results(real_values, lstm_predictions, kalman_predictions)
