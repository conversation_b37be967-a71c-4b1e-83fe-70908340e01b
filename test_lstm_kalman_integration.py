"""
测试LSTM-卡尔曼集成代码的基本功能
"""

import numpy as np
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt

# 导入修改后的类
from displacement_real_time_demo import DisplacementLSTM, LSTMKalmanFilter, LSTMKalmanRealTimePredictor

def test_lstm_kalman_integration():
    """测试LSTM-卡尔曼集成功能"""
    print("=== 测试LSTM-卡尔曼集成功能 ===")
    
    # 1. 生成测试数据
    print("1. 生成测试数据...")
    t = np.linspace(0, 4*np.pi, 200)
    test_data = np.sin(t) + 0.5*np.sin(3*t) + 0.1*np.random.randn(len(t))
    
    print(f"测试数据长度: {len(test_data)}")
    print(f"数据范围: {np.min(test_data):.3f} ~ {np.max(test_data):.3f}")
    
    # 2. 数据预处理
    print("2. 数据预处理...")
    scaler = MinMaxScaler(feature_range=(-1, 1))
    normalized_data = scaler.fit_transform(test_data.reshape(-1, 1)).flatten()
    
    # 3. 创建训练序列
    print("3. 创建训练序列...")
    window_size = 20
    
    def create_sequences(data, window_size):
        sequences, targets = [], []
        for i in range(len(data) - window_size):
            sequences.append(data[i:i+window_size])
            targets.append(data[i+window_size])
        return np.array(sequences), np.array(targets)
    
    X, y = create_sequences(normalized_data, window_size)
    X = torch.FloatTensor(X).unsqueeze(-1)
    y = torch.FloatTensor(y)
    
    print(f"训练序列形状: {X.shape}")
    print(f"目标序列形状: {y.shape}")
    
    # 4. 训练LSTM模型
    print("4. 训练LSTM模型...")
    model = DisplacementLSTM(input_size=1, hidden_size=32, output_size=1)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
    
    epochs = 50
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()
        
        if epoch % 10 == 0:
            print(f'Epoch {epoch}: Loss = {loss.item():.6f}')
    
    print("LSTM模型训练完成!")
    
    # 5. 测试LSTM-卡尔曼集成
    print("5. 测试LSTM-卡尔曼集成...")
    
    # 分割数据
    split_point = int(len(test_data) * 0.8)
    historical_data = test_data[:split_point]
    test_stream = test_data[split_point:]
    
    print(f"历史数据长度: {len(historical_data)}")
    print(f"测试流长度: {len(test_stream)}")
    
    # 创建集成预测器
    predictor = LSTMKalmanRealTimePredictor(model, scaler, window_size)
    predictor.initialize_buffer(historical_data)
    
    # 进行预测测试
    print("6. 进行预测测试...")
    predictions = []
    
    for i, true_value in enumerate(test_stream[:10]):  # 只测试前10个点
        lstm_pred, kalman_pred, pred_time = predictor.predict_next()
        predictions.append(kalman_pred)
        
        print(f"步骤 {i+1}: 真实值={true_value:.4f}, "
              f"LSTM预测={lstm_pred:.4f}, "
              f"卡尔曼集成={kalman_pred:.4f}, "
              f"预测时间={pred_time:.4f}s")
        
        # 添加观测值
        if i < len(test_stream) - 1:
            predictor.predict_next(true_value)
    
    print("\n=== 测试完成 ===")
    print("LSTM-卡尔曼集成功能正常工作!")
    
    return True

if __name__ == "__main__":
    try:
        success = test_lstm_kalman_integration()
        if success:
            print("\n✅ 所有测试通过!")
        else:
            print("\n❌ 测试失败!")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
